<template>
  <div class="page-container">
    <el-container class="main-container">
      <el-main>
        <el-card class="app-container">
          <template #header>
            <h1 class="header-title">Bibliographic Items Tagging Tool</h1>
            <el-text>An AI-driven tool for tagging bibliographic items using tags from a tag pool. (e.g.IsisCB's tag
              pool,
              etc) (Ver 0.3)</el-text>
          </template>

          <!-- Model Selection Dropdown -->
          <!-- Floating button for advanced settings -->
          <div class="floating-button">
            <div><el-button v-if="results.length" type="primary" circle @click="toggleAllResults"
                :icon="activeNames.length === paginatedResults.length ? ZoomOut : ZoomIn" /></div>
            <div><el-button type="primary" circle @click="handleSettingsToggle" :icon="Setting" /></div>
          </div>

          <SettingsDrawer ref="settingsDrawerRef" @settingsChanged="handleSettingsChanged"
            @drawerToggle="handleDrawerToggle" />

          <DataImporter :clearTrigger="clearTrigger" @itemsUpdated="handleItemsUpdated" />
          <ProcessingControls :biblioItems="biblioItems" :apiUrl="apiUrl" :selectedModel="selectedModel"
            @resultsUpdated="handleResultsUpdated" @allIndexedBiblioItemsUpdated="handleAllIndexedBiblioItemsUpdated"
            @clearAllData="handleClearAllData" @fetchAllTags="fetchAllTags" />

          <ResultsDisplay ref="resultsDisplayRef" :results="results" :allIndexedBiblioItems="allIndexedBiblioItems"
            :screenIsPortrait="screenIsPortrait" :tagNames="tagNames" :allTagCandidates="allTagCandidates"
            :apiAllTagsUrl="apiAllTagsUrl" :isFetchingTags="isFetchingTags" :hasLoadedTags="hasLoadedTags"
            @tagsUpdated="handleTagsUpdated" @stateUpdated="handleResultsDisplayStateUpdate" />

          <ExportControls :results="results" :allIndexedBiblioItems="allIndexedBiblioItems"
            :allTagCandidates="allTagCandidates" :deselectedTags="deselectedTags" :newTags="newTags"
            :currentSource="currentSource" :zoteroConfig="zoteroConfig" :zoteroBaseUrl="zoteroBaseUrl"
            :screenIsPortrait="screenIsPortrait" :apiAllTagsUrl="apiAllTagsUrl" :isFetchingTags="isFetchingTags"
            :hasLoadedTags="hasLoadedTags" @fetchAllTags="fetchAllTags" />
        </el-card>
      </el-main>
    </el-container>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import axios from 'axios'
import {
  ElContainer,
  ElMain,
  ElCard,
  ElButton,
  ElText,
  ElMessage
} from 'element-plus'
import { ZoomIn, ZoomOut, Setting } from '@element-plus/icons-vue'

// Import new components
import SettingsDrawer from './components/SettingsDrawer.vue'
import DataImporter from './components/DataImporter.vue'
import ProcessingControls from './components/ProcessingControls.vue'
import ResultsDisplay from './components/ResultsDisplay.vue'
import ExportControls from './components/ExportControls.vue'

// Settings state - now managed by SettingsDrawer component
const settingsDrawerRef = ref(null)
const screenIsPortrait = ref(false)
const selectedModel = ref('')
const apiUrl = ref('')
const apiAllTagsUrl = ref('')

const biblioItems = ref([]) // This is for storing the original items imported from data sources
const allIndexedBiblioItems = ref([]) // This is the indexed version of biblioItems. It will be populated when calling submitBiblioItems function.
const results = ref([]) // This is for storing the results from the API.

// Clear trigger for DataImporter
const clearTrigger = ref(0)

// Tag state for export functionality (received from ResultsDisplay)
const deselectedTags = ref(new Set())
const newTags = ref(new Map())

// Results display state for floating button functionality
const resultsDisplayRef = ref(null)
const currentPage = ref(1)
const pageSize = ref(10)

// Computed properties for pagination and toggle functionality
const paginatedResults = computed(() => {
  const startIndex = (currentPage.value - 1) * pageSize.value
  const endIndex = startIndex + pageSize.value
  return results.value.slice(startIndex, endIndex)
})

// Active names state - track which collapse items are expanded
const activeNames = ref([])

// TAG CONFIGURATION
const allTagCandidates = ref([]) // Store all tags from the curated tag pool
const tagNames = computed(() => allTagCandidates.value.map(tag => tag.name))
const isFetchingTags = ref(false)
const hasLoadedTags = ref(false) // Track if tags have been loaded


// Settings handlers
const handleSettingsChanged = (settings) => {
  screenIsPortrait.value = settings.screenIsPortrait
  selectedModel.value = settings.selectedModel
  apiUrl.value = settings.apiUrl
  apiAllTagsUrl.value = settings.apiAllTagsUrl
}

const handleSettingsToggle = () => {
  if (settingsDrawerRef.value) {
    settingsDrawerRef.value.toggleDrawer()
  }
}

const handleDrawerToggle = (isVisible) => {
  // Handle drawer visibility changes if needed
  // This can be used for any parent-level logic when drawer opens/closes
}

// Data source tracking (simplified)
const currentSource = ref('') // To track data source

// Zotero configuration (received from DataImporter when needed)
const zoteroConfig = ref(null)
const zoteroBaseUrl = ref('')

// Handle items updated from DataImporter
const handleItemsUpdated = (items, removedItems, source, zoteroData = null) => {
  biblioItems.value = items
  currentSource.value = source

  // Store Zotero configuration if provided
  if (source === 'Zotero' && zoteroData) {
    zoteroConfig.value = zoteroData.config
    zoteroBaseUrl.value = zoteroData.baseUrl
  } else if (source !== 'Zotero') {
    // Clear Zotero config when switching to non-Zotero sources
    zoteroConfig.value = null
    zoteroBaseUrl.value = ''
  }

  // Clear any existing results when new items are loaded
  results.value = []
  allIndexedBiblioItems.value = []
}

// Handle tags updated from ResultsDisplay
const handleTagsUpdated = (tagData) => {
  deselectedTags.value = tagData.deselectedTags
  newTags.value = tagData.newTags
}

// Handle results updated from ProcessingControls
const handleResultsUpdated = (newResults) => {
  results.value = newResults
}

// Handle indexed biblio items updated from ProcessingControls
const handleAllIndexedBiblioItemsUpdated = (indexedItems) => {
  allIndexedBiblioItems.value = indexedItems
}

// Handle clear all data from ProcessingControls
const handleClearAllData = () => {
  biblioItems.value = []
  allIndexedBiblioItems.value = []
  results.value = []
  currentSource.value = ''
  deselectedTags.value = new Set()
  newTags.value = new Map()
  // Clear Zotero config when clearing all data
  zoteroConfig.value = null
  zoteroBaseUrl.value = ''
  // Trigger DataImporter to clear its internal state
  clearTrigger.value++
}

// Function to fetch all tags
const fetchAllTags = async () => {
  if (hasLoadedTags.value) return // Don't fetch if already loaded

  try {
    isFetchingTags.value = true
    const response = await axios.get(`${apiAllTagsUrl.value}`)
    allTagCandidates.value = response.data
    hasLoadedTags.value = true
    ElMessage.success('Tag pool loaded successfully.')
  } catch (error) {
    console.error('Error fetching tags:', error)
    ElMessage.error('Failed to fetch tags')
    allTagCandidates.value = []
  } finally {
    isFetchingTags.value = false
  }
}













// Toggle all results function for floating button
const toggleAllResults = () => {
  if (resultsDisplayRef.value && resultsDisplayRef.value.toggleAllResults) {
    resultsDisplayRef.value.toggleAllResults()
  }
}

// Handle pagination and active names updates from ResultsDisplay
const handleResultsDisplayStateUpdate = (state) => {
  currentPage.value = state.currentPage
  pageSize.value = state.pageSize
  activeNames.value = state.activeNames
}

// Helper functions for components
const getAbstractByIndex = (index) => {
  const item = allIndexedBiblioItems.value.find(item => item.index === index)
  return item ? item.abstract : 'Abstract not found'
}
</script>

<style scoped>
.el-row {
  margin-bottom: 20px;
}

.el-divider--nowrap {
  text-align: center;
  white-space: nowrap;
  padding: 0;
  width: 100%;
  max-width: 100%;
}

.el-input-delimiter {
  width: 2rem;
}

.el-pagination {
  flex-wrap: wrap;
  gap: 5px;
}

.mark-position-select {
  width: 4rem;
  height: 1.8rem;
}

.center-content {
  display: flex;
  justify-content: center;
  /* Horizontally center child elements */
  align-items: center;
  /* Vertically center child elements */
  gap: 5px;
  /* Add spacing between child elements */
  flex-wrap: wrap;
  /* Ensure responsiveness if elements exceed available space */
  text-align: center;
}

.custom-input-number {
  width: 80px;
}

.button-container {
  display: flex;
  justify-content: center;
  /* Ensures the content inside <el-col> is centered */
  align-items: center;
  /* Aligns the button vertically within the container */
}

.page-container {
  min-height: 100vh;
  /* height: 100%; */
  width: 100%;
  display: flex;
  justify-content: center;
}

.main-container {
  width: 100%;
  max-width: 1200px;
  padding: 20px;
}

.app-container {
  width: 100%;
  height: 100%;
  margin: auto auto;
}


.header-title {
  margin: 0;
  font-size: 24px;
  color: var(--el-text-color-primary);
}

.article-form {
  width: 100%;
}

.article-input {
  margin-bottom: 30px;
  padding: 20px;
  border-radius: 8px;
  background-color: var(--el-bg-color);
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.form-item {
  margin-bottom: 20px;
}

.title-input,
.abstract-input {
  width: 100%;
}

.divider-title {
  font-size: 18px;
  font-weight: bold;
  color: var(--el-text-color-primary);
}

.action-buttons {
  display: flex;
  gap: 12px;
  margin: 20px 0;
  justify-content: center;
}

.csv-submit-button {
  margin-top: 20px;
}

.loading-message {
  text-align: center;
  margin: 20px 0;
  padding: 20px;
  background-color: var(--el-color-primary-light-9);
  border-radius: 4px;
  color: var(--el-text-color-primary);
}

.results {
  margin-top: 10px;
}

.wrap-text {
  white-space: pre-wrap;
  word-wrap: break-word;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.tag-item {
  margin: 0;
}

.el-tag {
  white-space: normal;
  /* Allow text to wrap */
  word-break: break-word;
  /* Break long words if necessary */
  min-height: 1.5rem;
  height: fit-content;
  max-width: 100%;
  /* Ensure the tag doesn't overflow its container */
}


/* For tag matching */
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

.loading-icon {
  width: 14px;
  height: 14px;
  animation: rotate 1s linear infinite;
}

.tag-input-container {
  position: relative;
  display: inline-block;
}

.tag-suggestions-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  width: flex;
  max-height: 300px;
  background: white;
  border: 1px solid var(--el-border-color);
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  z-index: 1000;
  overflow-y: auto;
}

.tag-suggestion-item {
  padding: 8px 12px;
  cursor: pointer;
  transition: background-color 0.3s;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.tag-suggestion-item:hover {
  background-color: var(--el-color-primary-light-9);
}

.loading-indicator {
  padding: 8px 12px;
  color: var(--el-text-color-secondary);
  text-align: center;
}

/* Add new styles for drag and drop */
.draggable-tag {
  cursor: move;
  transition: transform 0.2s;
}

.draggable-tag:hover {
  transform: scale(1.05);
}

.draggable-tag:active {
  cursor: grabbing;
}

.droppable-area {
  min-height: 40px;
  padding: 8px;
  border: 2px dashed transparent;
  border-radius: 4px;
  transition: all 0.3s;
}

.droppable-area:hover {
  border-color: var(--el-color-primary);
  background-color: var(--el-color-primary-light-9);
}

/* New styles for CSV preview */
.csv-section {
  margin: 20px 0;
  padding: 20px;
  background-color: var(--el-bg-color-overlay);
  border-radius: 8px;
}

.csv-preview {
  margin-top: 20px;
}

.preview-item {
  padding: 8px;
  margin: 4px 0;
  background-color: var(--el-bg-color);
  border-radius: 4px;
}

.preview-title {
  color: var(--el-text-color-primary);
  font-size: 14px;
}

.more-items {
  margin-top: 8px;
  color: var(--el-text-color-secondary);
  font-style: italic;
}

.download-section {
  margin-top: 20px;
  text-align: center;
}

.clickable-tag {
  cursor: pointer;
  transition: all 0.3s;
}

.deselected-tag {
  opacity: 0.5;
  text-decoration: line-through;
}

.new-tag {
  background-color: var(--el-color-success-light-9);
  border-color: var(--el-color-success);
  color: var(--el-color-success);
}

.tag-input {
  width: 280px;
  max-width: 100%;
  margin-left: 8px;
}

.floating-button {
  position: fixed;
  bottom: 30px;
  right: 30px;
  z-index: 1000;
  display: flex;
  align-items: center;
  flex-direction: column;
  gap: 10px;
}

.floating-button .el-button {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

/* Optional: Add hover effect for clickable tags */
.clickable-tag:hover {
  transform: scale(1.05);
}

.truncate-title {
  /* display: inline-block; */
  max-width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

:deep(.el-form-item__label) {
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 8px;
}

:deep(.el-card__header) {
  text-align: center;
  padding: 10px;
  background-color: var(--el-color-primary-light-9);
}

:deep(.el-collapse-item__header) {
  font-size: 16px;
  /* font-weight: bold; */
}

:deep(.el-descriptions__label) {
  font-weight: bold;
  width: 200px;
  padding-right: 16px;
}

:deep(.el-textarea__inner) {
  min-height: 120px !important;
}

:deep(.el-main) {
  padding: 0;
}

:deep(.el-descriptions__table) {
  width: 100%;
  /* Ensure the table takes full width of its container */
  max-width: 100%;
  /* Prevent overflow */
  table-layout: fixed;
  /* Ensure the table respects column widths */
}

:deep(.el-descriptions__cell) {
  word-break: break-word;
  /* Break long words to prevent overflow */
  white-space: normal;
  /* Allow text to wrap */
}


/* zotero-related */
.zotero-form {
  padding: 30px;
  background-color: var(--el-bg-color-overlay);
  border-radius: 0px;
  margin-bottom: 0px;
}

.source-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.fetch-button {
  width: 100%;
  margin-top: 16px;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

:deep(.el-collapse-item__header) {
  font-size: 16px;
}

:deep(.el-form-item__label) {
  font-weight: bold;
}
</style>