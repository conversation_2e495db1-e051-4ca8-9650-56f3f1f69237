<template>
  <div>
    <el-row justify="center">
      <el-col :span="16" class="center-content">
        <!-- Submit Button -->
        <el-button type="success" :icon="Check" @click="submitBiblioItems" :loading="isCSVLoading"
          :disabled="isCSVLoading || biblioItems.length === 0">
          {{ isCSVLoading ? `Processing items (${elapsedCSVTime}s)...` : 'Tag Items' }}
        </el-button>
        <el-text>in batch of</el-text>
        <el-input-number v-model="batchSize" :min="1"
          :max="Math.max(biblioItems.length, 1)" :disabled="isCSVLoading || biblioItems.length === 0"
          :placeholder="`${suggestedBatchSize}`" controls-position="right" class="custom-input-number" />
        <!-- Clear All Button -->
        <el-button type="danger" :icon="Delete" @click="clearAllItems"
          :disabled="isCSVLoading || biblioItems.length === 0">
          Clear All Items
        </el-button>
      </el-col>
    </el-row>

    <!-- Loading message -->
    <div v-if="isLoading || isCSVLoading" class="loading-message">
      <p>Generating tags... This may take a few minutes.</p>
      <p>Time elapsed: {{ isCSVLoading ? elapsedCSVTime : elapsedTime }} seconds</p>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onUnmounted } from 'vue'
import axios from 'axios'
import { ElRow, ElCol, ElButton, ElText, ElInputNumber, ElMessage, ElMessageBox } from 'element-plus'
import { Check, Delete } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  biblioItems: {
    type: Array,
    required: true
  },
  apiUrl: {
    type: String,
    required: true
  },
  selectedModel: {
    type: String,
    required: true
  }
})

// Emits
const emit = defineEmits([
  'resultsUpdated',
  'allIndexedBiblioItemsUpdated',
  'clearAllData',
  'fetchAllTags'
])

// Internal processing state
const isLoading = ref(false)
const isCSVLoading = ref(false)
const elapsedTime = ref(0)
const elapsedCSVTime = ref(0)
const batchSize = ref(null)
let timerInterval = null
let csvTimerInterval = null

// Computed properties
const suggestedBatchSize = computed(() => {
  const length = props.biblioItems.length;
  if (length === 0) {
    return 1; // Default when there are no items
  } else if (length <= 5) {
    return length; // Process all items if there are 5 or fewer
  } else if (length <= 50) {
    return Math.ceil(length / 5); // Split into ~5 batches for datasets between 6 and 50
  } else if (length <= 200) {
    return Math.ceil(length / 10); // Split into ~10 batches for datasets between 51 and 200
  } else {
    return Math.ceil(length / 20); // Split into ~20 batches for datasets larger than 200
  }
});

// Timer functions
const startTimer = (isCSV = false) => {
  if (isCSV) {
    elapsedCSVTime.value = 0
    csvTimerInterval = setInterval(() => {
      elapsedCSVTime.value++
    }, 1000)
  } else {
    elapsedTime.value = 0
    timerInterval = setInterval(() => {
      elapsedTime.value++
    }, 1000)
  }
}

const stopTimer = (isCSV = false) => {
  if (isCSV) {
    if (csvTimerInterval) {
      clearInterval(csvTimerInterval)
      csvTimerInterval = null
    }
  } else {
    if (timerInterval) {
      clearInterval(timerInterval)
      timerInterval = null
    }
  }
}

// Main processing function
const submitBiblioItems = async () => {
  try {
    isCSVLoading.value = true
    startTimer(true)

    // Clear previous results
    const results = []

    // VERY IMPORTANT: Index all items. After this step, allIndexedBiblioItems should be used instead of biblioItems.
    const allIndexedBiblioItems = props.biblioItems.map((item, idx) => ({
      ...item,
      index: idx,
    }))

    // Use the user's chosen batch size (or suggested if not set)
    const size = batchSize.value || suggestedBatchSize.value

    // Process items in batches
    for (let i = 0; i < allIndexedBiblioItems.length; i += size) {
      const batch = allIndexedBiblioItems.slice(i, i + size)
      const response = await axios.post(`${props.apiUrl}`, {
        model: props.selectedModel,
        items: batch.map(article => ({
          key: article.key,
          title: article.title,
          abstract: article.abstract,
          index: article.index
        }))
      })

      results.push(...response.data)
      ElMessage.success(`Batch ${Math.floor(i / size) + 1} tagged successfully!`)
      emit('fetchAllTags')
    }

    // Emit results and indexed items to parent
    emit('resultsUpdated', results)
    emit('allIndexedBiblioItemsUpdated', allIndexedBiblioItems)
  } catch (error) {
    console.error('Error batch-tagging articles:', error)
    ElMessage.error('An error occurred while batch-tagging articles.')
  } finally {
    stopTimer(true)
    isCSVLoading.value = false
  }
}

// Clear all items function
const clearAllItems = () => {
  ElMessageBox.confirm('This will remove all items. Continue?', 'Warning', {
    confirmButtonText: 'Yes',
    cancelButtonText: 'No',
    type: 'warning',
  }).then(() => {
    // Reset internal state
    batchSize.value = null

    // Emit to parent to clear all data
    emit('clearAllData')

    ElMessage({
      type: 'success',
      message: 'All items have been cleared',
      duration: 2000
    })
  }).catch((error) => {
    console.error('Error in clearAllItems:', error)
  })
}

// Cleanup timers on component unmount
onUnmounted(() => {
  stopTimer(false)
  stopTimer(true)
})
</script>

<style scoped>
.center-content {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
}

.custom-input-number {
  width: 120px;
}

.loading-message {
  text-align: center;
  margin: 20px 0;
  padding: 20px;
  background-color: var(--el-bg-color-overlay);
  border-radius: 8px;
}

.loading-message p {
  margin: 5px 0;
  color: var(--el-text-color-primary);
}
</style>
